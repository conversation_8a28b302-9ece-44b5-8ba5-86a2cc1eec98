<?php
include_once 'function_lib.php';

header('Content-Type: application/json');

$action = isset($_GET['action']) ? $_GET['action'] : '';

switch($action) {
    case 'sponsor':
        $refer_id = isset($_GET['refer_id']) ? trim($_GET['refer_id']) : '';
        
        if(empty($refer_id)) {
            echo json_encode(['invalid' => true]);
            exit;
        }
        
        // Check if sponsor exists and is active
        $sponsor_uid = registeredUserId($refer_id);
        if($sponsor_uid > 0) {
            $sponsor_details = get_user_details($sponsor_uid);
            if($sponsor_details && $sponsor_details->status == 0) {
                echo json_encode([
                    'invalid' => false,
                    'name' => $sponsor_details->name,
                    'uid' => $sponsor_uid
                ]);
            } else {
                echo json_encode(['invalid' => true]);
            }
        } else {
            echo json_encode(['invalid' => true]);
        }
        break;
        
    case 'email':
        $email = isset($_GET['email']) ? trim($_GET['email']) : '';
        
        if(empty($email)) {
            echo json_encode(['invalid' => true]);
            exit;
        }
        
        // Check email format
        if(!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            echo json_encode(['invalid' => true]);
            exit;
        }
        
        // Check email availability
        if(checkEmailAvailability($email)) {
            echo json_encode(['invalid' => true]); // Available (invalid = true means not taken)
        } else {
            echo json_encode(['invalid' => false]); // Not available (already exists)
        }
        break;
        
    case 'mobile':
        $mobile = isset($_GET['mobile']) ? trim($_GET['mobile']) : '';
        
        if(empty($mobile)) {
            echo json_encode(['invalid' => true]);
            exit;
        }
        
        // Check mobile format
        if(!checkMobile($mobile)) {
            echo json_encode(['invalid' => true]);
            exit;
        }
        
        // Check mobile availability
        if(checkMobileAvailability($mobile)) {
            echo json_encode(['invalid' => true]); // Available (invalid = true means not taken)
        } else {
            echo json_encode(['invalid' => false]); // Not available (already exists)
        }
        break;
        
    default:
        echo json_encode(['invalid' => true]);
        break;
}
?>
