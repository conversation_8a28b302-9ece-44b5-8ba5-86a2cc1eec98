<?php
include_once 'function_lib.php';

// Check if form is submitted
if($_SERVER['REQUEST_METHOD'] != 'POST') {
    setMessage('Invalid request method', 'error');
    redirect('signup.php');
    exit;
}

// Check if site is under maintenance
if(SITE_WORKING_STATUS) {
    setMessage('Site is under maintenance', 'error');
    redirect('signup.php');
    exit;
}

// Get form data
$refer_id = isset($_POST['refer_id']) ? trim($_POST['refer_id']) : '';
$position = isset($_POST['position']) ? trim($_POST['position']) : '';
$name = isset($_POST['name']) ? trim($_POST['name']) : '';
$email = isset($_POST['email']) ? trim($_POST['email']) : '';
$mobile = isset($_POST['mobile']) ? trim($_POST['mobile']) : '';
$password = isset($_POST['password']) ? trim($_POST['password']) : '';
$confirm_password = isset($_POST['confirm_password']) ? trim($_POST['confirm_password']) : '';
$checkbox = isset($_POST['checkbox']) ? $_POST['checkbox'] : '';

// Validation
$errors = [];

// Check referral ID
if(empty($refer_id)) {
    $errors[] = 'Referral ID is required';
} else {
    $sponsor_uid = registeredUserId($refer_id);
    if($sponsor_uid <= 0) {
        $errors[] = 'Invalid referral ID';
    }
}

// Check position
if(empty($position) || !in_array($position, ['L', 'R'])) {
    $errors[] = 'Please select a valid position';
}

// Check name
if(empty($name)) {
    $errors[] = 'Name is required';
} elseif(!preg_match('/^[a-zA-Z ]+$/', $name)) {
    $errors[] = 'Name should contain only letters and spaces';
} elseif(strlen($name) > 50) {
    $errors[] = 'Name should not exceed 50 characters';
}

// Check email
if(empty($email)) {
    $errors[] = 'Email is required';
} elseif(!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $errors[] = 'Invalid email format';
} elseif(!checkEmailAvailability($email)) {
    $errors[] = 'Email already exists';
}

// Check mobile
if(empty($mobile)) {
    $errors[] = 'Mobile number is required';
} elseif(!checkMobile($mobile)) {
    $errors[] = 'Mobile number must be 10 digits';
} elseif(!checkMobileAvailability($mobile)) {
    $errors[] = 'Mobile number already exists';
}

// Check password
if(empty($password)) {
    $errors[] = 'Password is required';
} elseif(!checkPassword($password)) {
    $errors[] = 'Password must be between 6 and 20 characters';
}

// Check confirm password
if(empty($confirm_password)) {
    $errors[] = 'Confirm password is required';
} elseif($password !== $confirm_password) {
    $errors[] = 'Passwords do not match';
}

// Check terms acceptance
if(empty($checkbox)) {
    $errors[] = 'You must accept the terms and conditions';
}

// If there are errors, redirect back with error message
if(!empty($errors)) {
    setMessage(implode('<br>', $errors), 'error');
    redirect('signup.php');
    exit;
}

// Generate login ID
$login_id = generateUniqueLoginId();

// Encrypt password
$encrypted_password = encryptPassword($password);

// Get current datetime
$datetime = date('Y-m-d H:i:s');

// Insert user into database
$query = "INSERT INTO user (
    login_id, 
    password, 
    name, 
    email, 
    mobile, 
    sponsor, 
    position, 
    datetime, 
    status,
    type
) VALUES (
    '$login_id',
    '$encrypted_password',
    '$name',
    '$email',
    '$mobile',
    '$sponsor_uid',
    '$position',
    '$datetime',
    0,
    'user'
)";

if(my_query($query)) {
    $new_uid = my_insert_id();
    
    // Set session variables
    $_SESSION['loginid'] = $login_id;
    $_SESSION['userid'] = $new_uid;
    $_SESSION['type'] = 'user';
    
    setMessage('Registration successful! Welcome to TETHER50', 'success');
    redirect('dashboard.php'); // Redirect to dashboard or appropriate page
} else {
    setMessage('Registration failed. Please try again.', 'error');
    redirect('signup.php');
}

// Function to generate unique login ID
function generateUniqueLoginId() {
    global $link;
    
    do {
        $login_id = 'T50' . str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT);
        $check = my_num_rows(my_query("SELECT uid FROM user WHERE login_id='$login_id'"));
    } while($check > 0);
    
    return $login_id;
}
?>
